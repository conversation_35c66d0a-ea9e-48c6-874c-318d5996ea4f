import logging
import re
import threading
from typing import Dict, Any, List
from werkzeug.datastructures import FileStorage

from models.qa_models import QAData, QAProcessResult, ProcessedImage
from clients.dify_client import DifyAPIClient
from utils.image_processor import ImageProcessor
from services.image_understanding_service import ImageUnderstandingService

logger = logging.getLogger(__name__)

class QAService:
    """Q&A处理服务"""

    def __init__(self, config):
        self.config = config
        self.dify_client = DifyAPIClient(config)
        self.image_processor = ImageProcessor(config)
        self.image_understanding_service = ImageUnderstandingService(config)
        # 添加锁来防止_handle_document_and_segment方法并发执行
        self._document_segment_lock = threading.Lock()
        
    def add_qa_to_knowledge_base(self, data_json: str, files: Dict[str, FileStorage]) -> Dict[str, Any]:
        """
        添加Q&A到知识库的主要处理流程
        """
        try:
            logger.info("开始处理Q&A添加请求")
            
            # 1. 解析数据
            qa_data = self._parse_qa_data(data_json)
            if not qa_data:
                return QAProcessResult(
                    result=False,
                    message="数据解析失败"
                ).to_dict()
            
            # 2. 验证数据
            validation_errors = qa_data.validate()
            if validation_errors:
                return QAProcessResult(
                    result=False,
                    message=f"数据验证失败: {'; '.join(validation_errors)}"
                ).to_dict()
            
            # 3. 验证上传的文件
            valid_files, file_errors = self.image_processor.validate_uploaded_files(files)
            if file_errors:
                logger.warning(f"文件验证警告: {file_errors}")

            # 4. 处理图片
            processed_qa = self._process_images(qa_data, files, valid_files)
            
            # 5. 去除HTML标签
            final_content = self._prepare_final_content(processed_qa)

            # 6. 处理文档和分段
            result = self._handle_document_and_segment(processed_qa.product_name, final_content)
            
            logger.info(f"Q&A处理完成: {result}")
            return result.to_dict()
            
        except Exception as e:
            logger.error(f"处理Q&A时发生错误: {e}", exc_info=True)
            return QAProcessResult(
                result=False,
                message=f"处理失败: {str(e)}"
            ).to_dict()
    
    def _parse_qa_data(self, data_json: str) -> QAData:
        """解析Q&A数据"""
        try:
            qa_data = QAData.from_json(data_json)
            logger.info(f"数据解析成功: 产品={qa_data.product_name}")
            return qa_data
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return None

    def _extract_imageId(self, images):
        imageId_list = []
        # 匹配 /img/ 和 .jpg/.jpeg/.png 之间的任意非斜杠内容
        pattern = r'/([^/]+)\.(?:jpg|jpeg|png)'
        for image_url in images:
            imageId_list.append(re.search(pattern, image_url).group(1))
        return imageId_list

    def _process_images(self, qa_data: QAData, files: Dict[str, FileStorage], valid_files: List[str]) -> QAData:
        """处理图片：上传到Dify、图片理解、URL替换"""
        try:
            logger.info("开始处理图片")
            
            # 提取HTML中的图片URL
            question_images = self.image_processor.extract_images_from_html(qa_data.question)
            answer_images = self.image_processor.extract_images_from_html(qa_data.answer)

            require_imageId_list = self._extract_imageId(question_images) + self._extract_imageId(answer_images)

            # 先校验所有需要的图片是否均存在
            for file_key in require_imageId_list:
                if file_key not in files:
                    raise Exception(f"找不到图片: {file_key}")
            # 上传文件并获取新的URL映射
            img_mapping = {}

            for file_key in require_imageId_list:
                file = files[file_key]
                # 上传文件到Dify
                file_id = self.dify_client.upload_file(file.stream, file.filename)
                if file_id:
                    # 构建新的URL,需要根据Dify的规则构建）
                    new_text = f"![image](/files/{file_id}/file-preview)"

                    # 图片理解（如果启用）
                    if self.config.get('ENABLE_IMAGE_UNDERSTANDING', True):
                        understanding_text = self.image_understanding_service.understand_image(file.stream)
                        if understanding_text:
                            new_text += f"[图片描述: {understanding_text}]"

                    img_mapping[file_key] = new_text

            # 替换HTML中的图片URL
            updated_question = self.image_processor.replace_image_urls_in_html(qa_data.question, img_mapping)
            updated_answer = self.image_processor.replace_image_urls_in_html(qa_data.answer, img_mapping)
            
            logger.info(f"图片处理完成，处理了{len(img_mapping)}个图片")
            
            return QAData(
                product_name=qa_data.product_name,
                question=updated_question,
                answer=updated_answer
            )
            
        except Exception as e:
            raise Exception(f"处理图片时发生错误: {e}")
    
    def _prepare_final_content(self, qa_data: QAData) -> str:
        """准备最终的文档内容（去除HTML标签）"""
        try:
            # 去除HTML标签
            question_text = self.image_processor.remove_html_tags(qa_data.question)
            answer_text = self.image_processor.remove_html_tags(qa_data.answer)
            
            # 组装最终内容
            final_content = f"问题描述：{question_text}\n\n\n解决办法：{answer_text}"
            
            logger.info("最终内容准备完成")
            return final_content
            
        except Exception as e:
            logger.error(f"准备最终内容时发生错误: {e}")
            return f"问题: {qa_data.question}\n\n\n解答: {qa_data.answer}"
    
    def _handle_document_and_segment(self, product_name: str, content: str) -> QAProcessResult:
        """处理文档和分段 - 使用锁防止并发执行"""
        with self._document_segment_lock:
            logger.info(f"获取文档处理锁，开始处理产品: {product_name}")
            try:
                # 生成文档标题
                document_title = self.dify_client.generate_document_title(product_name)
                logger.info(f"生成文档标题: {document_title}")

                # 查询文档是否存在
                doc_info = self.dify_client.find_document_by_title(document_title)

                document_id = None
                segment_id = None

                if doc_info:
                    # 文档已存在，添加分段
                    logger.info(f"文档已存在: {doc_info.document_id}")
                    document_id = doc_info.document_id
                    segment_id = self.dify_client.add_segment_to_document(document_id, content)

                    if not segment_id:
                        return QAProcessResult(
                            result=False,
                            message="添加分段失败"
                        )

                else:
                    # 文档不存在，创建新文档
                    logger.info("创建新文档")
                    metadata = {"product_name": product_name}
                    document_id = self.dify_client.create_document_by_text(document_title, content, metadata)

                    if not document_id:
                        return QAProcessResult(
                            result=False,
                            message="创建文档失败"
                        )

                    # 获取分段ID
                    segments = self.dify_client.get_document_segments(document_id)
                    if segments:
                        segment_id = segments[0].segment_id

                logger.info(f"文档处理完成，释放锁。文档ID: {document_id}, 分段ID: {segment_id}")
                return QAProcessResult(
                    result=True,
                    message="Q&A添加成功",
                    document_id=document_id,
                    segment_id=segment_id,
                )

            except Exception as e:
                logger.error(f"处理文档和分段时发生错误: {e}")
                return QAProcessResult(
                    result=False,
                    message=f"处理文档失败: {str(e)}"
                )
            finally:
                logger.info("释放文档处理锁")
