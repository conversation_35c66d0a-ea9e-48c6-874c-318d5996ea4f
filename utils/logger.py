import logging
import os
from logging.handlers import RotatingFile<PERSON>andler

def setup_logger(app):
    """设置应用日志"""
    
    # 创建logs目录
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 设置日志级别
    if app.config['DEBUG']:
        app.logger.setLevel(logging.DEBUG)
    else:
        app.logger.setLevel(logging.INFO)
    
    # 创建文件处理器
    file_handler = RotatingFileHandler(
        'logs/aiserver.log',
        maxBytes=10240000,  # 10MB
        backupCount=10,
        encoding='utf-8'  # 指定UTF-8编码
    )
    
    # 设置日志格式
    formatter = logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    )
    file_handler.setFormatter(formatter)
    
    # 添加处理器
    app.logger.addHandler(file_handler)
    
    # 控制台输出（开发环境）
    if app.config['DEBUG']:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        app.logger.addHandler(console_handler)
    
    app.logger.info('AIServer启动')

def get_logger(name):
    """获取指定名称的日志器"""
    logger = logging.getLogger(name)
    
    if not logger.handlers:
        # 创建处理器
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s %(name)s %(levelname)s: %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    
    return logger
