import requests
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from models.qa_models import DocumentInfo, SegmentInfo

logger = logging.getLogger(__name__)

class DifyAPIClient:
    """Dify API客户端"""
    
    def __init__(self, config):
        self.base_url = config.get('DIFY_API_BASE_URL')
        self.app_api_key = config.get('DIFY_APP_API_KEY')
        self.kb_api_key = config.get('DIFY_KB_API_KEY')
        self.dataset_id = config.get('DIFY_DATASET_ID')
        self.timeout = config.get('REQUEST_TIMEOUT')
        
        if not self.app_api_key:
            logger.warning("Dify API密钥未配置")
        if not self.kb_api_key:
            logger.warning("Dify 知识库API密钥未配置")

    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'Authorization': f'Bearer {self.kb_api_key}',
            'Content-Type': 'application/json'
        }

    def _get_file_headers(self) -> Dict[str, str]:
        """获取文件上传请求头"""
        return {
            'Authorization': f'Bearer {self.app_api_key}'
        }

    def _get_cookies(self) -> Dict[str, str]:
        """获取请求cookies"""
        return {
            'portal_code': 'sansec',
            'AiPortalToken': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJwb3J0YWxDb2RlIjoic2Fuc2VjIiwidXNlclR5cGUiOiIzMCIsInVzZXJJZCI6MTk2NzUxMTgxMzk2ODQyNDk2MiwidXVpZCI6ImQxM2U5MzRlZTI1YTQ2NGFiZDg2N2U2MDQzZjMyNWRkIn0.vR56W5Suz-WoJbCmAUWn4VFNAG-lO8j7kEDy26ZlvYg'
        }
    
    def upload_file(self, file_data, filename: str) -> Optional[str]:
        """
        上传文件到Dify
        返回文件ID
        """
        try:
            url = f"{self.base_url}/files/upload"
            
            files = {
                'file': (filename, file_data, 'application/octet-stream')
            }
            
            data = {
                'user': 'SecSales'
            }
            
            response = requests.post(
                url,
                headers=self._get_file_headers(),
                cookies=self._get_cookies(),
                files=files,
                data=data,
                timeout=self.timeout,
                verify=False
            )
            
            if response.status_code == 201:
                result = response.json()
                file_id = result.get('id')
                logger.info(f"文件上传成功: {filename} -> {file_id}")
                return file_id
            else:
                logger.error(f"文件上传失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"上传文件时发生错误: {e}")
            return None
    
    def find_document_by_title(self, title: str) -> Optional[DocumentInfo]:
        """
        根据标题查询知识库文档
        """
        try:
            url = f"{self.base_url}/datasets/{self.dataset_id}/documents"
            params = {
                'keyword': title
            }
            
            response = requests.get(
                url,
                headers=self._get_headers(),
                cookies=self._get_cookies(),
                params=params,
                timeout=self.timeout,
                verify=False
            )
            
            if response.status_code == 200:
                result = response.json()
                documents = result.get('data', [])

                if documents and len(documents) > 0:
                    logger.info(f"找到{len(documents)}个文档")
                    doc = documents[0]
                    doc_info = DocumentInfo(
                        document_id=doc.get('id'),
                        title=doc.get('name'),
                    )
                    logger.info(f"找到匹配文档: {title} -> {doc_info.document_id}")
                    return doc_info

                logger.info(f"未找到匹配文档: {title}")
                return DocumentInfo(document_id="", title=title, exists=False)
            else:
                logger.error(f"查询文档失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"查询文档时发生错误: {e}")
            return None
    
    def create_document_by_text(self, title: str, content: str, metadata: Dict[str, Any] = None) -> Optional[str]:
        """
        通过文本创建文档
        返回文档ID
        """
        try:
            url = f"{self.base_url}/datasets/{self.dataset_id}/document/create-by-text"
            
            payload = {
                'name': title,
                'text': content,
                'indexing_technique': 'high_quality',
                "doc_form": "hierarchical_model",
                "process_rule": {
                    "mode": "hierarchical",
                    "rules": {
                        "pre_processing_rules": [
                            {
                                "id": "remove_extra_spaces",
                                "enabled": False
                            }
                        ],
                        "segmentation": {
                            "separator": "parent_split",
                            "max_tokens": 2000
                        },
                        "parent_mode": "paragraph",
                        "subchunk_segmentation": {
                            "separator": "\n\n\n",
                            "max_tokens": 500
                        }
                    }
                }
            }
            
            if metadata:
                payload['metadata'] = metadata
            
            response = requests.post(
                url,
                headers=self._get_headers(),
                cookies=self._get_cookies(),
                json=payload,
                timeout=self.timeout,
                verify=False
            )
            
            if response.status_code == 200:
                result = response.json()
                document_id = result.get('document', {}).get('id')
                logger.info(f"文档创建成功: {title} -> {document_id}")
                return document_id
            else:
                logger.error(f"创建文档失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"创建文档时发生错误: {e}")
            return None
    
    def add_segment_to_document(self, document_id: str, content: str) -> Optional[str]:
        """
        向文档添加分段
        返回分段ID
        """
        try:
            url = f"{self.base_url}/datasets/{self.dataset_id}/documents/{document_id}/segments"
            
            payload = {
                'segments': [
                    {
                        'content': content,
                        'keywords': []
                    }
                ]
            }
            
            response = requests.post(
                url,
                headers=self._get_headers(),
                cookies=self._get_cookies(),
                json=payload,
                timeout=self.timeout,
                verify=False
            )
            
            if response.status_code == 200:
                result = response.json()
                segments = result.get('data', [])
                if segments:
                    segment_id = segments[0].get('id')
                    logger.info(f"分段添加成功: {document_id} -> {segment_id}")
                    return segment_id
            
            logger.error(f"添加分段失败: {response.status_code} - {response.text}")
            return None
            
        except Exception as e:
            logger.error(f"添加分段时发生错误: {e}")
            return None
    
    def get_document_segments(self, document_id: str) -> List[SegmentInfo]:
        """
        获取文档的分段列表
        """
        try:
            url = f"{self.base_url}/datasets/{self.dataset_id}/documents/{document_id}/segments"
            
            response = requests.get(
                url,
                headers=self._get_headers(),
                cookies=self._get_cookies(),
                timeout=self.timeout,
                verify=False
            )
            
            if response.status_code == 200:
                result = response.json()
                segments_data = result.get('data', [])
                
                segments = []
                for seg_data in segments_data:
                    segment = SegmentInfo(
                        segment_id=seg_data.get('id'),
                        document_id=document_id,
                        content=seg_data.get('content', '')
                    )
                    segments.append(segment)
                
                logger.info(f"获取到{len(segments)}个分段")
                return segments
            else:
                logger.error(f"获取分段失败: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"获取分段时发生错误: {e}")
            return []
    
    def generate_document_title(self, product_name: str) -> str:
        """
        根据产品名称和年月生成文档标题
        """
        current_date = datetime.now()
        year = current_date.year
        month = current_date.month
        return f"{year}年{month}月{product_name}FAQ知识库"
