from dotenv import load_dotenv

load_dotenv()

class Config:
    """应用配置类"""
    
    # Flask配置
    DEBUG = False

    # 认证配置
    AUTH_TOKEN = 'SecSales-9XbR2pL7vNqY4wTzKmP1x'
    
    # Dify API配置
    # DIFY_API_BASE_URL = 'http://10.0.107.252:9009/v1'
    # DIFY_APP_API_KEY = 'app-lyqj362FOyQpZZhtpkQtb6YE'
    # DIFY_KB_API_KEY = 'dataset-XEKue5YD8K4oFSVoYTO4gC4B'
    # DIFY_DATASET_ID = '8fab06cd-91ce-46d0-b787-aaf5fcf13079'
    DIFY_API_BASE_URL = 'https://10.0.8.180/v1'
    DIFY_APP_API_KEY = 'app-pwKy7ZlDfNS5kqwtZyzLxJ9c'
    DIFY_KB_API_KEY = 'dataset-SxsAvMG682hIi19klzCYPKwe'
    DIFY_DATASET_ID = 'a3619b1a-898a-4846-be65-7d324bf0545b'

    # 文件上传配置
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}
    
    # 业务配置
    MAX_IMAGES_PER_QA = 10  # Q和A各自最多10张图片
    MAX_Q_LENGTH = 1000      # 问题描述最大字数
    MAX_A_LENGTH = 2000     # 解决方案最大字数
    REQUEST_TIMEOUT = 300   # 5分钟超时

    # 图片理解功能开关
    ENABLE_IMAGE_UNDERSTANDING = True
    VL_MODEL_URL = 'https://ai.secsign.online:3003/v1/chat/completions'
    VL_MODEL_API_KEY = 'sk-oevf0oKVuzIq8XzkX2cEDx3tkn9tWhRQonZItgrU3AliTjzE'
    VL_MODEL_PROMPT = "简要描述这张图片的内容，不做过多引申。"

    @staticmethod
    def init_app(app):
        """初始化应用配置"""
